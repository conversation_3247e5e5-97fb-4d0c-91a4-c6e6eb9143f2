# iframe预加载优化方案

## 概述

针对iframe预加载需求，我们实现了一套完整的预加载管理方案，确保iframe能够在不阻塞主应用流程的情况下提前加载，并在路由匹配时实现"秒开"效果。

## 主要改进

### 1. 架构优化

**原实现问题：**
- 每次路由切换都进行DOM移动操作
- 缺乏统一的预加载管理
- 没有加载状态和错误处理

**新实现优势：**
- 统一的预加载管理器组件
- 基于Hook的状态管理
- 完善的加载状态和错误处理
- 更好的性能和用户体验

### 2. 核心组件

#### IframePreloader 组件 (`src/components/IframePreloader/index.tsx`)
- 应用级别的iframe预加载管理器
- 在应用启动时创建所有需要预加载的iframe
- 提供工具函数用于显示/隐藏iframe
- 支持配置化管理多个iframe

#### useIframePreloader Hook (`src/hooks/useIframePreloader.ts`)
- 封装iframe预加载逻辑
- 提供加载状态、错误处理
- 自动根据路由状态控制iframe显示
- 支持超时处理

#### 优化后的IframePageView组件 (`src/pages/Iframe/index.tsx`)
- 使用Hook管理状态
- 添加加载动画和错误处理
- 平滑的显示/隐藏过渡效果

### 3. 实现特点

#### 预加载策略
```typescript
// 配置需要预加载的iframe
const IFRAME_CONFIGS: IframeConfig[] = [
  {
    id: 'edap-iframe',
    src: edapIframeSrc,
    preload: true,
    targetPath: '/edap-workspace'
  }
];
```

#### 智能显示控制
- 根据当前路由自动显示/隐藏iframe
- 保持iframe在后台的预加载状态
- 避免重复加载和DOM操作开销

#### 状态管理
- `isLoading`: 是否正在加载
- `isLoaded`: 是否加载完成
- `shouldShow`: 是否应该显示
- `error`: 错误信息

#### 用户体验优化
- 加载动画提示
- 平滑的透明度过渡
- 错误状态处理和重试功能
- 超时保护机制

### 4. 使用方式

#### 在应用中集成
```tsx
// src/index.tsx
root.render(
  <AppProvider>
    <IframePreloader>
      <App />
    </IframePreloader>
  </AppProvider>
);
```

#### 在页面组件中使用
```tsx
// src/pages/Iframe/index.tsx
const {isLoading, isLoaded, shouldShow, error} = useIframePreloader({
  iframeId: 'edap-iframe',
  targetPath: '/edap-workspace',
  currentPath: location.pathname,
  timeout: 30000
});
```

### 5. 性能优势

1. **减少DOM操作**: 避免频繁的DOM移动操作
2. **预加载效果**: iframe在应用启动时就开始加载
3. **内存优化**: iframe保持在DOM中但隐藏，避免重复创建
4. **路由切换优化**: 路由匹配时几乎"秒开"

### 6. 扩展性

#### 支持多iframe预加载
可以轻松添加更多需要预加载的iframe：

```typescript
const IFRAME_CONFIGS: IframeConfig[] = [
  {
    id: 'edap-iframe',
    src: edapIframeSrc,
    preload: true,
    targetPath: '/edap-workspace'
  },
  {
    id: 'another-iframe',
    src: 'https://another-app.com',
    preload: true,
    targetPath: '/another-path'
  }
];
```

#### 配置化管理
- 支持动态配置iframe列表
- 可配置预加载策略
- 支持不同的显示条件

### 7. 错误处理

- 加载超时处理
- 网络错误处理
- 跨域访问处理
- 用户友好的错误提示

### 8. 最佳实践

1. **合理设置超时时间**: 根据iframe内容大小调整
2. **监控加载状态**: 可以添加埋点监控iframe加载性能
3. **错误降级**: 提供备用方案或重试机制
4. **内存管理**: 在应用卸载时清理iframe资源

## 总结

这套iframe预加载方案解决了原有实现的性能问题，提供了更好的用户体验和开发体验。通过统一的管理器和Hook封装，使得iframe预加载变得更加可控和可维护。

主要优势：
- ✅ 真正的预加载效果
- ✅ 不阻塞主应用流程
- ✅ 路由匹配时"秒开"
- ✅ 完善的状态管理
- ✅ 良好的错误处理
- ✅ 易于扩展和维护
