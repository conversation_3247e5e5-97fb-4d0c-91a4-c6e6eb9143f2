/**
 * iframe 内嵌 EDAP 页面
 */

import React, {FC, useEffect} from 'react';

import './index.less';

export const edapIframeSrc = `${window.location.origin}/edap/`;

const IframePageView: FC<{url: string}> = (props) => {
  // 定位到 EDAP产品首页
  const {url = edapIframeSrc} = props;

  useEffect(() => {
    const iframe = document.getElementById('edap-iframe');
    const container = document.querySelector('.iframe-page-box');

    // 将 iframe 添加到页面显示容器中
    container.appendChild(iframe);
    iframe.style.display = 'block';

    return () => {
      // 隐藏 iframe 并移除挂载（可选）
      iframe.style.display = 'none';
      const iframeDom = container.removeChild(iframe);
      document.body.append(iframeDom);
    };
  }, []);

  return (
    <div className="iframe-page-box">
      <iframe src={url} width="100%" height="100%"></iframe>
    </div>
  );
};

export default React.memo(IframePageView);
