/**
 * iframe 内嵌 EDAP 页面
 */

import React, {FC, useEffect} from 'react';
import {useLocation} from 'react-router-dom';
import {Loading} from 'acud';
import {useIframePreloader} from '@hooks/useIframePreloader';
import './index.less';

const IframeEdapPageView: FC = () => {
  const location = useLocation();
  console.log('location :>> ', location);
  // 使用 iframe 预加载管理器
  const {isLoading, isLoaded, shouldShow, error} = useIframePreloader({
    iframeId: 'edap-iframe',
    targetPath: '/edap-workspace',
    currentPath: location.pathname,
    containerId: 'iframe-div-box',
    timeout: 30000 // 30秒超时
  });

  console.log('1、加载中  isLoading:>> ', isLoading);
  console.log('2、加载完成  isLoaded :>> ', isLoaded);
  console.log('3、是否可以展示 shouldShow :>> ', shouldShow);
  console.log('4、error :>> ', error);
  console.log('\n');

  // 如果当前路由不匹配，不渲染组件
  if (!shouldShow) {
    return null;
  }

  // 如果有错误，显示错误信息 TODO: 尽量不搞加载失败
  if (error) {
    return (
      <div className="iframe-page-box">
        <div className="iframe-error-overlay">
          <div className="error-content">
            <h3>加载失败</h3>
            <p>{error}</p>
            <button onClick={() => window.location.reload()} className="retry-button">
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="iframe-page-box">
      {isLoading && (
        <div className="iframe-loading-overlay">
          <Loading loading />
          {/* <div className="loading-text">正在加载结构化空间...</div> */}
        </div>
      )}
      {/* 占位容器，实际的iframe会被动态移动到这里 */}
      <div
        className="iframe-div-box"
        style={{
          width: '100%',
          height: '100%',
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out'
        }}
      />
    </div>
  );
};

export default React.memo(IframeEdapPageView);
