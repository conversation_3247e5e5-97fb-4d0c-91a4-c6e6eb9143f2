import './public-path';
import 'acud/dist/acud.min.css';
import '@baidu/bce-react-toolkit/es/styles/_overwrite_acud.css';

import '@styles/global.less';
import '@styles/dbsc.less';
import '@styles/tailwind.css';
import '@styles/acud.less';
import flags from './flags';
import {createRoot} from 'react-dom/client';
import {
  AppProvider,
  FrameworkProvider,
  i18nInstance,
  I18nProvider,
  I18nUtil,
  toolkitConfig
} from '@baidu/bce-react-toolkit';

import App from './App';
import IframePreloader from '@components/IframePreloader';

const DatabuilderPrivateSwitch = flags.DatabuilderPrivateSwitch;

if (DatabuilderPrivateSwitch) {
  toolkitConfig.init({
    publicPath: window.appPublicPath,
    appTitle: 'DataBuilder'
  });
} else {
  toolkitConfig.init({
    // APP_ENABLE_I18N 等全局变量由 CLI 中 @baidu/cba-preset-console-react 预设中注入
    enableI18n: APP_ENABLE_I18N,
    enableIndependentI18n: APP_ENABLE_INDEPENDENT_I18N,
    isEmbed: APP_IS_EMBED_MODE,
    publicPath: window.appPublicPath,
    supportedLanguageTypes: APP_ALLOWED_LANGUAGE_TYPES || [],
    appTitle: 'DataBuilder'
  });
}

export async function bootstrap(initData: any) {
  let i18nUtil;
  // 非私有化处理toolkit国际化
  if (!DatabuilderPrivateSwitch) {
    i18nUtil = new I18nUtil();
    await i18nUtil.init();
  }

  // 获取根元素
  const container = document.querySelector('#main');

  // 创建根
  const root = createRoot(container as HTMLElement);
  root.render(
    DatabuilderPrivateSwitch ? (
      <AppProvider>
        <IframePreloader>
          <App />
        </IframePreloader>
      </AppProvider>
    ) : (
      <I18nProvider i18n={i18nInstance} defaultNS={'translation'} i18nUtil={i18nUtil}>
        <FrameworkProvider frameworkData={initData}>
          <AppProvider>
            <IframePreloader>
              <App />
            </IframePreloader>
          </AppProvider>
        </FrameworkProvider>
      </I18nProvider>
    )
  );
}

// 私有化需自执行 bootstrap 方法
DatabuilderPrivateSwitch && bootstrap({});
