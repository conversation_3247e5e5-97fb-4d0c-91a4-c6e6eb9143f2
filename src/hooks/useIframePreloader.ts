/**
 * iframe预加载管理Hook
 */

import {useEffect, useState, useCallback} from 'react';
import {showIframe as showIframeUtil, hideIframe as hideIframeUtil} from '@components/IframePreloader';

interface IframePreloaderOptions {
  /** iframe的ID */
  iframeId: string;
  /** 目标路由路径 */
  targetPath: string;
  /** 当前路由路径 */
  currentPath: string;
  /** iframe 父标签容器的ID */
  containerId: string;
  /** 预加载超时时间（毫秒） */
  timeout?: number;
}

interface IframePreloaderState {
  /** 是否正在加载 */
  isLoading: boolean;
  /** 是否加载完成 */
  isLoaded: boolean;
  /** 是否应该显示iframe */
  shouldShow: boolean;
  /** 错误信息 */
  error: string | null;
}

export const useIframePreloader = ({
  iframeId,
  targetPath,
  currentPath,
  containerId,
  timeout = 30000
}: IframePreloaderOptions): IframePreloaderState & {
  showIframe: () => void;
  hideIframe: () => void;
} => {
  console.log('🍌 :>> ', '🍌');
  const [state, setState] = useState<IframePreloaderState>({
    isLoading: true,
    isLoaded: false,
    shouldShow: false,
    error: null
  });

  console.log('shouldShow :>> ', currentPath, targetPath);
  // 检查当前路由是否匹配目标路径
  const shouldShow = currentPath === targetPath;

  // 显示iframe
  const showIframe = useCallback(() => {
    const container = document.querySelector(`#${containerId}`);
    if (container) {
      showIframeUtil(iframeId, container);
    }
  }, [iframeId]);

  // 隐藏iframe
  const hideIframe = useCallback(() => {
    hideIframeUtil(iframeId);
  }, [iframeId]);

  // 监听iframe加载状态
  useEffect(() => {
    console.log('苹果 :>> 🍎');
    const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
    console.log('iframe :>> ', iframe);

    // 不能存在的情况下
    if (!iframe) {
      setState((prev) => ({
        ...prev,
        error: `iframe with id "${iframeId}" not found`,
        isLoading: false
      }));
      return;
    }

    let timeoutId: number;

    const handleLoad = () => {
      clearTimeout(timeoutId);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        isLoaded: true,
        error: null
      }));
    };

    const handleError = () => {
      clearTimeout(timeoutId);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        isLoaded: false,
        error: 'iframe failed to load'
      }));
    };

    try {
      handleLoad();
    } catch (error) {
      handleError();
    }

    return () => {
      clearTimeout(timeoutId);
      hideIframe();
    };
  }, [iframeId, timeout]);

  // 根据路由状态控制iframe显示
  // useEffect(() => {
  //   setState((prev) => ({
  //     ...prev,
  //     shouldShow
  //   }));

  //   if (shouldShow) {
  //     showIframe();
  //   } else {
  //     hideIframe();
  //   }
  // }, [shouldShow, showIframe, hideIframe]);

  return {
    ...state,
    showIframe,
    hideIframe
  };
};
